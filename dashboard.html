<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Quản Lý</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(239, 68, 68, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.06) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
            padding: 20px 0;
            transition: all 0.3s ease;
            border-right: 1px solid rgba(226, 232, 240, 0.8);
        }

        .sidebar-header {
            padding: 0 20px 30px;
            border-bottom: 1px solid rgba(226, 232, 240, 0.6);
            margin-bottom: 20px;
        }

        .sidebar-header h2 {
            color: #1e293b;
            font-size: 24px;
            font-weight: 700;
            background: linear-gradient(135deg, #3b82f6, #ef4444);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            color: #64748b;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
            margin: 2px 0;
            border-radius: 0 12px 12px 0;
            position: relative;
        }

        .menu-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(239, 68, 68, 0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 0 12px 12px 0;
        }

        .menu-item:hover::before {
            opacity: 1;
        }

        .menu-item:hover {
            background: rgba(255, 255, 255, 0.8);
            border-left-color: #3b82f6;
            color: #1e293b;
            transform: translateX(4px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }

        .menu-item.active {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(239, 68, 68, 0.05));
            border-left-color: #ef4444;
            color: #1e293b;
            font-weight: 600;
            transform: translateX(4px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.15);
        }

        .menu-item i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .header {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px 35px;
            margin-bottom: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid rgba(226, 232, 240, 0.6);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #3b82f6, #ef4444, #10b981);
        }

        .breadcrumb {
            color: #64748b;
            font-size: 14px;
            font-weight: 500;
        }

        .breadcrumb span {
            color: #ef4444;
            font-weight: 700;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            color: #1e293b;
            font-weight: 600;
        }

        .user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #ef4444);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
            position: relative;
        }

        .user-avatar::before {
            content: '';
            position: absolute;
            inset: 2px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ef4444, #3b82f6);
            z-index: -1;
        }

        /* Form Card */
        .form-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 45px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
            max-width: 900px;
            border: 1px solid rgba(226, 232, 240, 0.6);
            position: relative;
            overflow: hidden;
        }

        .form-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #ef4444, #10b981, #f59e0b);
        }

        .form-header {
            margin-bottom: 35px;
        }

        .form-title {
            font-size: 32px;
            color: #1e293b;
            margin-bottom: 15px;
            font-weight: 700;
            background: linear-gradient(135deg, #1e293b, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .form-subtitle {
            color: #64748b;
            font-size: 16px;
            font-weight: 500;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 25px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        label {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 14px;
            letter-spacing: 0.5px;
        }

        input, select {
            padding: 16px 22px;
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            color: #1e293b;
            font-weight: 500;
        }

        input:focus, select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
            background: rgba(255, 255, 255, 1);
            transform: translateY(-1px);
        }

        input:hover, select:hover {
            border-color: #cbd5e1;
            background: rgba(255, 255, 255, 1);
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background: linear-gradient(135deg, #3b82f6, #ef4444);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .form-actions {
            display: flex;
            gap: 18px;
            margin-top: 45px;
        }

        .btn {
            padding: 16px 32px;
            border: none;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #ef4444);
            color: white;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(59, 130, 246, 0.4);
        }

        .btn-secondary {
            background: rgba(248, 250, 252, 0.9);
            color: #64748b;
            border: 2px solid #e2e8f0;
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(241, 245, 249, 1);
            border-color: #cbd5e1;
            color: #1e293b;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .info-section {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(239, 68, 68, 0.05));
            border-radius: 18px;
            padding: 25px;
            margin-top: 35px;
            border: 1px solid rgba(226, 232, 240, 0.6);
            backdrop-filter: blur(10px);
            position: relative;
        }

        .info-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #3b82f6, #ef4444);
            border-radius: 18px 18px 0 0;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            padding: 8px 0;
        }

        .info-label {
            font-weight: 600;
            color: #1e293b;
            font-size: 14px;
        }

        .info-value {
            color: #64748b;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-value i {
            color: #3b82f6;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-chart-line"></i> Admin Panel</h2>
            </div>
            <nav>
                <a href="#" class="menu-item">
                    <i class="fas fa-chart-pie"></i>
                    Quản lý log
                </a>
                <a href="#" class="menu-item active">
                    <i class="fas fa-users"></i>
                    Quản lý tài khoản
                </a>
                <a href="#" class="menu-item">
                    <i class="fas fa-user-friends"></i>
                    Quản lý tin nhắn
                </a>
                <a href="#" class="menu-item">
                    <i class="fas fa-users-cog"></i>
                    Quản lý khách hàng
                </a>
                <a href="#" class="menu-item">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
                <a href="#" class="menu-item">
                    <i class="fas fa-chart-bar"></i>
                    Báo cáo thống kê
                </a>
                <a href="#" class="menu-item">
                    <i class="fas fa-database"></i>
                    Cài đặt
                </a>
                <a href="#" class="menu-item" style="margin-top: 20px; border-top: 1px solid rgba(0,0,0,0.1); padding-top: 20px;">
                    <i class="fas fa-sign-out-alt"></i>
                    Đăng xuất
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <div>
                    <div class="breadcrumb">
                        Quản lý tài khoản / Tài khoản / <span>18503942</span> / Chỉnh sửa
                    </div>
                </div>
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <span>Admin</span>
                </div>
            </div>

            <!-- Form -->
            <div class="form-card">
                <div class="form-header">
                    <h1 class="form-title">18503942 NGUYEN VAN DUY</h1>
                    <div style="display: flex; align-items: center; gap: 15px; margin-top: 20px;">
                        <span style="background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 8px 20px; border-radius: 25px; font-size: 13px; font-weight: 600; box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3); letter-spacing: 0.5px;">Hiệu lực</span>
                        <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <div style="background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(245, 158, 11, 0.05)); border: 1px solid rgba(251, 191, 36, 0.3); border-radius: 16px; padding: 18px; margin-bottom: 30px; color: #92400e; backdrop-filter: blur(10px); position: relative; overflow: hidden;">
                    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 2px; background: linear-gradient(90deg, #f59e0b, #fbbf24);"></div>
                    <i class="fas fa-exclamation-triangle" style="margin-right: 10px; color: #f59e0b;"></i>
                    <span style="font-weight: 600;">Yêu cầu người dùng khi đăng nhập</span>
                    <button style="float: right; background: none; border: none; color: #f59e0b; cursor: pointer; padding: 4px; border-radius: 8px; transition: all 0.3s ease;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="username">Tên đăng nhập*</label>
                            <input type="text" id="username" value="18503942" readonly>
                        </div>
                        <div class="form-group">
                            <label for="fullname">Họ và tên*</label>
                            <input type="text" id="fullname" value="NGUYEN VAN DUY">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">Email*</label>
                            <input type="email" id="email" value="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label for="unit">Đơn vị quản lý*</label>
                            <select id="unit">
                                <option value="ha-dong-t/o">HÀ ĐÔNG T/O</option>
                                <option value="other">Khác...</option>
                            </select>
                        </div>
                    </div>

                    <div class="info-section">
                        <div class="info-row">
                            <span class="info-label">Người tạo</span>
                            <span class="info-value"><i class="fas fa-user"></i> NGUYEN NGOC BAO TRAM</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Ngày tạo</span>
                            <span class="info-value"><i class="fas fa-calendar"></i> 15/08/2024 15:39:00</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Người cập nhật gần nhất</span>
                            <span class="info-value"><i class="fas fa-user"></i> NGUYEN NGOC BAO TRAM</span>
                        </div>
                        <div class="info-row" style="margin-bottom: 0;">
                            <span class="info-label">Ngày cập nhật gần nhất</span>
                            <span class="info-value"><i class="fas fa-calendar"></i> 15/08/2024 15:39:00</span>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Lưu
                        </button>
                        <button type="button" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            Hủy
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Toggle menu active state
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.menu-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Dữ liệu đã được lưu thành công!');
        });
    </script>
</body>
</html>
